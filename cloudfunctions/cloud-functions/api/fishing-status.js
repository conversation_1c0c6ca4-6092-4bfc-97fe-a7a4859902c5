/**
 * 摸鱼状态相关API
 */

const fishingStatusDB = require('../db/fishing-status')
const cacheDB = require('../db/cache')
const { getCurrentUser, validateAdminPermission } = require('../utils/auth')
const { success, error, wrapAsync, validateRequired } = require('../utils/response')


/**
 * 开始摸鱼状态记录
 */
exports.startFishingStatus = wrapAsync(async (params = {}) => {
  validateRequired(params, ['workId', 'startMinutes', 'workSegment'])

  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 检查是否已有活跃的摸鱼状态
  const hasActiveResult = await fishingStatusDB.hasActiveFishingStatus(user._id)
  if (hasActiveResult.hasActive) {
    // 如果已有状态，先删除旧的再创建新的
    console.log(`用户 ${user._id} 已有摸鱼状态，先清理旧状态`)
    await fishingStatusDB.deleteByUserId(user._id)
  }

  // 验证工作段数据
  const { workSegment } = params
  if (!workSegment || typeof workSegment.start !== 'number' || typeof workSegment.end !== 'number') {
    return error('工作段数据格式错误')
  }

  // 验证开始时间是否在工作段内
  if (params.startMinutes < workSegment.start || params.startMinutes > workSegment.end) {
    return error('摸鱼开始时间不在工作段内')
  }

  // 创建摸鱼状态记录
  const fishingData = {
    userId: user._id,
    openid: user.openid,
    workId: params.workId,
    startMinutes: params.startMinutes,
    workSegment: workSegment,
    remark: params.remark || ''
  }

  const result = await fishingStatusDB.createFishingStatus(fishingData)

  if (!result.success) {
    return result
  }

  // 清除摸鱼人数缓存
  await cacheDB.deleteByKey('fishing_count')
  console.log('[FishingAPI] 开始摸鱼，已清除缓存')

  console.log(`用户 ${user._id} 开始摸鱼状态记录成功`)
  return success(null, '摸鱼状态记录成功')
})

/**
 * 结束摸鱼状态记录
 */
exports.endFishingStatus = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 删除用户的摸鱼状态
  const result = await fishingStatusDB.deleteByUserId(user._id)

  if (!result.success) {
    return result
  }

  // 清除摸鱼人数缓存
  await cacheDB.deleteByKey('fishing_count')
  console.log('[FishingAPI] 结束摸鱼，已清除缓存')

  console.log(`用户 ${user._id} 结束摸鱼状态记录成功`)
  return success(null, '摸鱼状态清除成功')
})

/**
 * 获取当前摸鱼人数（使用数据库缓存）
 */
exports.getCurrentFishingCount = wrapAsync(async (params = {}) => {
  const cacheKey = 'fishing_count'

  try {
    // 尝试获取缓存
    const cacheResult = await cacheDB.get(cacheKey)

    if (cacheResult.success && cacheResult.data) {
      cacheResult.data.cleanedExpired = null;
      console.log(`[FishingAPI] 返回缓存的摸鱼人数: ${cacheResult.data.count}`)
      return success(cacheResult.data, '获取当前摸鱼人数成功（缓存）')
    }

    // 缓存未命中，重新查询数据库
    console.log('[FishingAPI] 缓存未命中，查询数据库')
    const result = await fishingStatusDB.getCurrentFishingCount()

    if (!result.success) {
      return result
    }

    // 设置缓存（10秒后过期）
    const expireTime = new Date(Date.now() + 10 * 1000) // 10秒
    await cacheDB.set(cacheKey, result.data, expireTime)

    console.log(`[FishingAPI] 获取当前摸鱼人数: ${result.data.count}人，清理过期状态: ${result.data.cleanedExpired}个`)
    return success(result.data, '获取当前摸鱼人数成功')

  } catch (error) {
    console.error('[FishingAPI] 获取摸鱼人数失败:', error)
    return error(error.message || '获取摸鱼人数失败')
  }
})

/**
 * 清理过期的摸鱼状态（管理员功能）
 * 需要管理员权限
 */
exports.cleanupExpiredFishingStatus = wrapAsync(async (params = {}) => {
  // 验证管理员权限
  const adminValidation = await validateAdminPermission()
  if (!adminValidation.success) {
    return error(adminValidation.message)
  }

  const result = await fishingStatusDB.cleanupExpiredStatus()

  if (!result.success) {
    return result
  }

  // 清除缓存
  cachedFishingCount = null
  cacheTime = 0

  console.log(`清理过期摸鱼状态完成，清理数量: ${result.data.cleanedCount}`)
  return success(result.data, '清理过期摸鱼状态完成')
})

/**
 * 获取用户当前摸鱼状态
 */
exports.getUserFishingStatus = wrapAsync(async (params = {}) => {
  // 获取当前用户
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 查找用户的摸鱼状态
  const result = await fishingStatusDB.findByUserId(user._id)

  if (!result.success) {
    return result
  }

  const hasStatus = result.data !== null
  const statusData = hasStatus ? {
    startTime: result.data.startTime,
    startMinutes: result.data.startMinutes,
    workSegment: result.data.workSegment,
    remark: result.data.remark
  } : null

  return success({
    hasStatus,
    status: statusData
  }, hasStatus ? '用户有活跃的摸鱼状态' : '用户没有活跃的摸鱼状态')
})
