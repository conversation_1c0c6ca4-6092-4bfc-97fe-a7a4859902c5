# 实时收入飘动效果功能

## 功能概述

为实时收入显示添加了类似游戏中数值变化的飘动效果，当收入每增加1元时，会在实时收入数字的右上角显示一个"+¥1.00"的飘动数字，该数字会向上移动并逐渐消失。

## 功能特点

### 1. 触发条件
- 每当实时收入累计增加1元时自动触发
- 支持同时显示多个飘动效果（最多5个）
- 只有收入增加时才触发，收入减少不会触发

### 2. 视觉效果
- 飘动数字从实时收入右上角开始
- 向上移动约80rpx的距离
- 包含淡入、放大、淡出的动画效果
- 动画持续时间约2秒
- 使用绿色字体表示收入增加

### 3. 性能优化
- 限制同时存在的飘动元素数量（最多5个）
- 动画结束后自动清理DOM元素
- 使用CSS动画而非JavaScript动画，性能更好

## 技术实现

### 1. 数据结构
```javascript
data: {
  floatingNumbers: [],        // 当前显示的飘动数字数组
  lastIncome: 0,              // 上次的收入值（数字）
  accumulatedIncrease: 0,     // 累计增加值
  floatingIdCounter: 0        // 飘动元素ID计数器
}
```

### 2. 核心方法
- `checkIncomeChange(newIncome)` - 检测收入变化并触发飘动
- `triggerFloatingNumber(amount)` - 创建飘动数字（检查开关状态）
- `removeFloatingNumber(id)` - 移除完成的飘动数字
- `resetIncomeTracking()` - 重置收入跟踪
- `loadDashboardConfig()` - 加载配置（包含动画开关）

### 3. 配置管理
- 在仪表盘设置中提供开关控制
- 默认启用飘动动画
- 配置保存到本地存储
- 支持实时开关切换

### 4. 触发时机
- 在实时收入更新回调中检测收入变化
- 支持普通模式和跨日期模式
- 在组件初始化和工作切换时重置跟踪
- 检查配置开关状态决定是否触发

## 使用说明

### 1. 功能开关
在仪表盘1的设置中可以开启或关闭飘动动画效果：
- 点击右上角设置按钮
- 找到"收入飘动动画"开关
- 默认状态为开启

### 2. 自动触发
正常使用小程序时，当实时收入增加时会自动触发飘动效果。

## 样式定制

### 1. 飘动容器
```css
.floating-numbers-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 200rpx;
  height: 100rpx;
  pointer-events: none;
  overflow: hidden;
}
```

### 2. 飘动数字
```css
.floating-number {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 32rpx;
  font-weight: 700;
  color: #22c55e;
  opacity: 0;
  transform: translateY(0);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 2rpx 4rpx rgba(34, 197, 94, 0.3);
  white-space: nowrap;
}
```

### 3. 动画效果
```css
@keyframes floatingUp {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  20% {
    opacity: 1;
    transform: translateY(-10rpx) scale(1.1);
  }
  100% {
    opacity: 0;
    transform: translateY(-80rpx) scale(0.8);
  }
}
```

## 注意事项

1. **性能考虑**：限制了同时存在的飘动元素数量，避免过多DOM元素影响性能
2. **用户体验**：飘动位置有轻微随机偏移，避免多个效果完全重叠
3. **兼容性**：使用标准CSS动画，兼容性良好
4. **重置机制**：在工作切换和组件初始化时会重置收入跟踪，避免错误触发

## 未来扩展

1. 可以考虑添加不同金额的不同效果（如5元、10元时的特殊效果）
2. 可以添加音效配合视觉效果
3. 可以添加用户设置来控制是否启用此功能
4. 可以考虑添加更多动画变化（如不同颜色、不同轨迹等）
