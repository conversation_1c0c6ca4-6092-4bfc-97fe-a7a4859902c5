# 飘动动画设置指南

## 功能概述

仪表盘1现在支持收入飘动动画的开关控制，用户可以根据个人喜好启用或禁用这个功能。

## 如何使用

### 1. 打开设置
1. 在仪表盘1界面，点击右上角的设置按钮（齿轮图标）
2. 设置模态框会弹出

### 2. 找到动画开关
在设置列表中找到"收入飘动动画"选项：
- **标题**：收入飘动动画
- **描述**：每增加1元时显示飘动效果
- **开关**：右侧的切换开关

### 3. 调整设置
- **开启**：向右滑动开关，开关变为蓝色
- **关闭**：向左滑动开关，开关变为灰色

### 4. 保存设置
点击"保存"按钮，设置会立即生效并保存到本地存储。

## 功能说明

### 开启状态
- 实时收入每增加1元时会显示飘动的"+¥1.00"效果
- 支持同时显示多个飘动数字

### 关闭状态
- 不会显示任何飘动效果
- 收入计算和显示不受影响

## 默认设置

- **默认状态**：开启
- **首次使用**：自动启用飘动动画
- **设置保存**：本地存储，下次打开应用时保持上次设置

## 技术细节

### 配置存储
```javascript
dashboardConfig: {
  enableFloatingAnimation: true // 默认开启
}
```

### 检查逻辑
每次触发飘动效果前都会检查配置：
```javascript
if (!this.data.dashboardConfig.enableFloatingAnimation) {
  return // 直接返回，不显示效果
}
```

### 实时生效
- 设置修改后立即生效
- 无需重启应用
- 保存到本地存储持久化

## 用户体验

### 优点
- **个性化**：用户可以根据喜好选择是否启用
- **性能考虑**：关闭动画可以减少DOM操作
- **专注工作**：有些用户可能觉得动画分散注意力

### 设计理念
- **默认开启**：大多数用户会喜欢这个有趣的功能
- **易于关闭**：不喜欢的用户可以轻松关闭
- **清晰说明**：设置项有明确的描述文字

## 故障排除

### 动画不显示
1. 检查设置是否开启
2. 确认收入确实在增加
3. 检查是否达到1元的触发阈值

### 设置不保存
1. 确认点击了"保存"按钮
2. 检查本地存储权限
3. 重新打开应用验证设置

## 相关文档

- [飘动效果技术文档](./floating-income-effect.md)
- [仪表盘配置说明](./dashboard-configuration.md)

## 更新日志

### v1.0.0
- 新增收入飘动动画开关
- 支持实时开关切换
- 添加设置说明文字
- 默认启用动画效果
