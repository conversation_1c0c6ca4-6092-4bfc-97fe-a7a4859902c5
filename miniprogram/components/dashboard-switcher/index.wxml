<!-- 主题切换器组件 -->
<view wx:if="{{show}}" class="switcher-overlay {{visible ? 'show' : ''}}" bindtap="onHide">
  <view class="switcher-content" catchtap="onModalContentTap">
    <view class="switcher-header">
      <text class="switcher-title">选择主题</text>
      <view class="switcher-close" bindtap="onHide">✕</view>
    </view>

    <view class="dashboard-list">
      <view
        wx:for="{{availableDashboards}}"
        wx:key="id"
        class="dashboard-option {{item.id === currentDashboard ? 'active' : ''}}"
        bindtap="onSelectDashboard"
        data-dashboard="{{item.id}}">
        <view class="dashboard-icon">{{item.icon}}</view>
        <view class="dashboard-info">
          <text class="dashboard-name">{{item.name}}</text>
          <text class="dashboard-desc">{{item.description}}</text>
        </view>
        <view wx:if="{{item.id === currentDashboard}}" class="current-badge">当前</view>
      </view>
    </view>
  </view>
</view>
