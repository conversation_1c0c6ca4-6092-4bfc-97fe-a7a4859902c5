<!--日历页面 - 集成版本-->
<view class="calendar-container">
  <!-- 没有工作履历时的引导界面 -->
  <view wx:if="{{!hasWorkHistory}}" class="no-work-guide">
    <view class="guide-content">
      <view class="guide-icon">📅</view>
      <view class="guide-title">设置工作计划</view>
      <view class="guide-text">
        <text>要开始使用工作计划功能，</text>
        <text>请先添加您的工作履历。</text>
      </view>
      <button class="guide-btn" bindtap="goToWorkHistory">
        <text class="btn-icon">✨</text>
        <text>添加工作履历</text>
      </button>
    </view>
  </view>

  <!-- 有工作履历时的正常日历界面 -->
  <view wx:else class="calendar-content">
    <!-- 头部 -->
    <view class="calendar-header">
      <view class="header-title-row">
        <view class="header-title">
          <text class="title-icon">💼</text>
          <text class="title-text">当前工作</text>
        </view>
        <view class="work-switch-btn" bindtap="goToWorkHistory">
          <text>切换</text>
        </view>
      </view>

      <!-- 当前工作履历显示 -->
      <view class="current-work-info">
        <view class="work-info-content">
          <text class="work-status-icon" style="color: {{workStatusColor}}">{{workStatusIcon}}</text>
          <text class="work-name">{{currentWorkDisplayName}}</text>
        </view>

        <!-- 工作天数信息 -->
        <view class="work-days-info" wx:if="{{workDaysInfo.length > 0}}">
          <view class="work-days-tag" wx:for="{{workDaysInfo}}" wx:key="index" style="color: {{item.color}}; border-color: {{item.color}}; background-color: {{item.color}}1A;">
            <text>{{item.text}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历主体 -->
    <view class="calendar-main">
      <!-- 月份导航 -->
      <view class="month-nav">
        <view class="nav-btn" bind:tap="onPreviousYear">
          <text class="year-nav-icon">‹‹</text>
        </view>
        <view class="nav-btn" bind:tap="onPreviousMonth">
          <text class="month-nav-icon">‹</text>
        </view>

        <view class="month-title-container" bind:tap="onToday">
          <view class="month-title">
            <text class="year">{{currentYear}}年</text>
            <text class="month">{{currentMonth}}月</text>
          </view>
          <view class="back-to-today-hint">点击回到今天</view>
        </view>

        <view class="nav-btn" bind:tap="onNextMonth">
          <text class="month-nav-icon">›</text>
        </view>
        <view class="nav-btn" bind:tap="onNextYear">
          <text class="year-nav-icon">››</text>
        </view>
      </view>

      <!-- 星期标题 -->
      <view class="weekdays">
        <view class="weekday" wx:for="{{weekdays}}" wx:key="index">
          <text>{{item}}</text>
        </view>
      </view>

      <!-- 日历网格 -->
      <view class="calendar-grid">
        <view
          class="day-cell {{item ? 'day-active' : 'day-empty'}} {{item && item.isToday ? 'day-today' : ''}} {{item && item.isSelected ? 'day-selected' : ''}} {{item && item.hasData ? 'day-has-data' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}} {{item && item.status ? 'day-status-' + item.status : ''}} {{item && item.holidayInfo ? 'holiday-type-' + item.holidayInfo.type : 'holiday-type-normal'}}"
          wx:for="{{calendarDays}}"
          wx:key="index"
          bind:tap="onDateTap"
          data-day="{{item ? item.day : ''}}"
          style="{{item && item.statusConfig ? '--status-color: ' + item.statusConfig.color + '; --status-bg-color: ' + item.statusConfig.backgroundColor + '; --status-border-color: ' + item.statusConfig.borderColor + ';' : ''}}">
          
          <text wx:if="{{item}}" class="day-number">{{item.day}}</text>
          
          <!-- 状态指示器 -->
          <view wx:if="{{item && item.status && item.statusConfig}}" class="status-indicator">
            <text class="status-icon">{{item.statusConfig.icon}}</text>
          </view>
          
          <!-- 节假日指示器 -->
          <view wx:if="{{item && item.holidayInfo && item.holidayInfo.type !== 'normal' && item.holidayInfo.type !== 'weekend'}}" class="holiday-indicator">
            <text class="holiday-text">{{item.holidayInfo.name}}</text>
          </view>
          
          <!-- 数据指示点 - 已移除，改用状态背景色 -->
          
          <!-- 今天标识 -->
          <view wx:if="{{item && item.isToday}}" class="today-indicator">今</view>

          <!-- 发薪日标识 -->
          <view wx:if="{{item && item.isPayDay}}" class="payday-indicator">💰</view>
        </view>
      </view>
    </view>

    <!-- 日期信息卡片 -->
    <view class="date-info-card" wx:if="{{selectedDate}}">
      <view class="day-header">
        <view class="day-title">
          <text class="section-icon">🕐</text>
          <text class="section-title-text">时间安排</text>
        </view>

        <view class="add-adjustment-btn edit-plan" bind:tap="onSetSchedule">
          <text class="add-icon">⚙️</text>
          <text class="add-text">编辑</text>
        </view>
      </view>

      <!-- 卡片顶部居中显示日期和节假日信息 -->
      <view class="card-date-info">
        <view class="card-date-title">
          <text class="date-text">{{selectedDateText}} - {{selectedDateWeekday}} - {{selectedDateStatus}}</text>
        </view>
      </view>

      <!-- 时间可视化 -->
      <view class="chart-section" wx:if="{{displaySegments.length > 0}}">
        <time-chart
          segments="{{chartSegments}}"
          fishes="{{displayFishes}}"
          selected-date="{{selectedDateKey}}"
          height="{{140}}"
          show-current-time="{{true}}">
        </time-chart>
      </view>

      <!-- 时间统计 -->
      <view class="time-stats-section" wx:if="{{displaySegments.length > 0}}">

        <view class="time-stats-grid">
          <view class="time-stat-item work-stat">
            <view class="stat-icon">💼</view>
            <view class="stat-info">
              <view class="stat-label">工作</view>
              <view class="stat-value">{{workTime}}</view>
              <view class="stat-sub-value">{{currencySymbol}}{{workIncome || '0.00'}}</view>
            </view>
          </view>

          <view class="time-stat-item rest-stat">
            <view class="stat-icon">☕</view>
            <view class="stat-info">
              <view class="stat-label">休息</view>
              <view class="stat-value">{{restTime}}</view>
              <view class="stat-sub-value">{{restPercentage || '0%'}}</view>
            </view>
          </view>

          <view class="time-stat-item overtime-stat">
            <view class="stat-icon">🌙</view>
            <view class="stat-info">
              <view class="stat-label">加班</view>
              <view class="stat-value">{{overtimeTime}}</view>
              <view class="stat-sub-value">{{currencySymbol}}{{overtimeIncome || '0.00'}}</view>
            </view>
          </view>

          <view class="time-stat-item income-stat" wx:if="{{selectedDayData}}">
            <view class="stat-icon">💰</view>
            <view class="stat-info">
              <view class="stat-label">日收入</view>
              <view class="stat-value">{{currencySymbol}}{{selectedDayData.dailyIncomeText || '0.00'}}</view>
              <view class="stat-sub-value">{{currencySymbol}}{{averageHourlyRate || '0.00'}}/h</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间安排内容 -->
      <view class="segments-section">
        <view wx:if="{{displaySegments.length === 0}}" class="empty-segments">
          <view class="empty-icon">📝</view>
          <view class="empty-text">暂无时间安排</view>
          <view class="empty-tip">点击右上角按钮设置工作计划</view>
        </view>

        <view wx:else class="segments-list">
          <view class="segment-item segment-{{item.type}}" wx:for="{{displaySegments}}" wx:key="id">
            <view class="segment-left">
              <view class="type-badge type-{{item.type}}">{{item.typeText}}</view>
              <view class="segment-time">
                <text class="time-text">{{item.startTime}} - {{item.endTime}}</text>
                <text class="duration-text">{{item.duration}}</text>
              </view>
            </view>

            <view class="segment-right" wx:if="{{item.income > 0}}">
              <view class="segment-income">
                <text class="income-text">{{currencySymbol}}{{item.incomeText}}</text>
                <text class="rate-text" wx:if="{{item.hourlyRateText}}">{{currencySymbol}}{{item.hourlyRateText}}/h</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 摸鱼卡片 -->
    <view class="fishing-records-card" wx:if="{{selectedDate}}">
      <view class="section-title">
        <view class="section-title-left">
          <text class="section-icon">🐟</text>
          <text>摸鱼</text>
        </view>
        <view class="add-adjustment-btn fishing" bind:tap="onAddFishing">
          <text class="add-icon">+</text>
          <text class="add-text">添加</text>
        </view>
      </view>

      <!-- 摸鱼统计 -->
      <view class="fishing-stats-section" wx:if="{{displayFishes.length > 0}}">
        <view class="time-stats-grid">
          <view class="time-stat-item fishing-count-stat">
            <view class="stat-icon">🐟</view>
            <view class="stat-info">
              <view class="stat-label">摸鱼次数</view>
              <view class="stat-value">{{fishingStats.count}}次</view>
            </view>
          </view>

          <view class="time-stat-item fishing-duration-stat">
            <view class="stat-icon">⏱️</view>
            <view class="stat-info">
              <view class="stat-label">摸鱼时长</view>
              <view class="stat-value">{{fishingStats.duration}}</view>
            </view>
          </view>

          <view class="time-stat-item fishing-income-stat">
            <view class="stat-icon">💰</view>
            <view class="stat-info">
              <view class="stat-label">摸鱼收入</view>
              <view class="stat-value">{{currencySymbol}}{{fishingStats.income}}</view>
            </view>
          </view>
        </view>
      </view>

      <view wx:if="{{displayFishes.length > 0}}" class="fishing-list">
        <view class="fishing-item" wx:for="{{displayFishes}}" wx:key="id" bind:tap="onEditFishing" data-id="{{item.id}}">
          <view class="fishing-left">
            <view class="type-badge type-fishing">摸鱼</view>
            <view class="fishing-content">
              <view class="fishing-title">
                <text class="time-text">{{item.startTime}} - {{item.endTime}}</text>
                <text class="duration-text">{{item.duration}}</text>
              </view>
            </view>
          </view>

          <view class="fishing-center" wx:if="{{item.remark}}">
            <text class="remark-text">{{item.remark}}</text>
          </view>

          <view class="fishing-right">
            <view class="fishing-value">
              <text class="value-text">{{currencySymbol}}{{item.hourlyRateText}}/h</text>
              <text class="rate-text">{{currencySymbol}}{{item.fishingValueText}}</text>
            </view>
          </view>

          <view class="fishing-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>

      <view wx:else class="empty-fishing">
        <text class="empty-icon">🐟</text>
        <text class="empty-text">暂无摸鱼记录</text>
        <text class="empty-tip">点击上方"添加"按钮创建摸鱼记录</text>
      </view>
    </view>

    <!-- 收入调整卡片 -->
    <view class="income-adjustment-card" wx:if="{{selectedDate}}">
      <view class="section-title">
        <view class="section-title-left">
          <text class="section-icon">💰</text>
          <text>收入和扣款</text>
        </view>
        <view class="adjustment-actions">
          <view class="add-adjustment-btn income" bind:tap="onAddExtraIncome">
            <text class="add-icon">+</text>
            <text class="add-text">收入</text>
          </view>
          <view class="add-adjustment-btn deduction" bind:tap="onAddDeduction">
            <text class="add-icon">-</text>
            <text class="add-text">扣款</text>
          </view>
        </view>
      </view>

      <!-- 收入调整统计 -->
      <view wx:if="{{selectedDayAdjustmentSummary && (selectedDayAdjustmentSummary.extraIncomeItems.length > 0 || selectedDayAdjustmentSummary.deductionItems.length > 0)}}" class="adjustment-stats-section">
        <view class="time-stats-grid">
          <view class="time-stat-item income-stat" wx:if="{{selectedDayAdjustmentSummary.extraIncome > 0}}">
            <view class="stat-icon">📈</view>
            <view class="stat-info">
              <view class="stat-label">收入</view>
              <view class="stat-value">+{{currencySymbol}}{{selectedDayAdjustmentSummary.extraIncome}}</view>
            </view>
          </view>

          <view class="time-stat-item deduction-stat" wx:if="{{selectedDayAdjustmentSummary.deductions > 0}}">
            <view class="stat-icon">📉</view>
            <view class="stat-info">
              <view class="stat-label">扣款</view>
              <view class="stat-value">-{{currencySymbol}}{{selectedDayAdjustmentSummary.deductions}}</view>
            </view>
          </view>

          <view class="time-stat-item net-stat" wx:if="{{selectedDayAdjustmentSummary.netAdjustment !== 0}}">
            <view class="stat-icon">{{selectedDayAdjustmentSummary.netAdjustment > 0 ? '💰' : '💸'}}</view>
            <view class="stat-info">
              <view class="stat-label">结余</view>
              <view class="stat-value">{{selectedDayAdjustmentSummary.netAdjustment > 0 ? '+' : ''}}{{currencySymbol}}{{selectedDayAdjustmentSummary.netAdjustment}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 收入列表 -->
      <view wx:if="{{selectedDayAdjustmentSummary && selectedDayAdjustmentSummary.extraIncomeItems.length > 0}}" class="adjustment-list">
        <view class="adjustment-category-title">
          <text class="category-icon">📈</text>
          <text>收入</text>
        </view>
        <view class="adjustment-tile income-tile" wx:for="{{selectedDayAdjustmentSummary.extraIncomeItems}}" wx:key="id" bind:tap="onEditExtraIncome" data-item="{{item}}">
          <view class="tile-indicator income-indicator"></view>
          <view class="tile-content">
            <view class="tile-left">
              <view class="tile-type">{{item.typeText}}</view>
              <view class="tile-desc" wx:if="{{item.desc}}">{{item.desc}}</view>
            </view>
            <view class="tile-right">
              <view class="tile-amount income-text">+{{currencySymbol}}{{item.amount || 0}}</view>
            </view>
          </view>
          <view class="tile-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>

      <!-- 扣款列表 -->
      <view wx:if="{{selectedDayAdjustmentSummary && selectedDayAdjustmentSummary.deductionItems.length > 0}}" class="adjustment-list">
        <view class="adjustment-category-title">
          <text class="category-icon">📉</text>
          <text>扣款</text>
        </view>
        <view class="adjustment-tile deduction-tile" wx:for="{{selectedDayAdjustmentSummary.deductionItems}}" wx:key="id" bind:tap="onEditDeduction" data-item="{{item}}">
          <view class="tile-indicator deduction-indicator"></view>
          <view class="tile-content">
            <view class="tile-left">
              <view class="tile-type">{{item.typeText}}</view>
              <view class="tile-desc" wx:if="{{item.desc}}">{{item.desc}}</view>
            </view>
            <view class="tile-right">
              <view class="tile-amount deduction-text">-{{currencySymbol}}{{item.amount || 0}}</view>
            </view>
          </view>
          <view class="tile-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{!selectedDayAdjustmentSummary || (selectedDayAdjustmentSummary.extraIncomeItems.length === 0 && selectedDayAdjustmentSummary.deductionItems.length === 0)}}" class="empty-adjustments">
        <text class="empty-icon">💰</text>
        <text class="empty-text">暂无收入调整</text>
        <text class="empty-tip">点击上方按钮添加额外收入或扣款</text>
      </view>
    </view>

    <!-- 操作按钮卡片 -->
    <view class="actions-card">
      <view class="actions-content">
        <!-- 常用操作 -->
        <view class="action-btn primary" bind:tap="onBatchCopy">
          <text class="btn-icon">✨</text>
          <text>批量复制时间安排到其他日期</text>
        </view>
        <view class="action-btn secondary" bind:tap="debugForceUpdateHolidays">
          <text class="btn-icon">🔄</text>
          <text>重新获取最新节假日数据</text>
        </view>

        <!-- 危险操作 -->
        <view wx:if="{{selectedDate && shouldShowClearButton}}" class="danger-section">
          <view class="action-btn danger" bind:tap="onClearDay">
            <text class="btn-icon">🗑️</text>
            <text>清除当日所有数据</text>
          </view>
          <view class="danger-tip">
            <text class="tip-icon">⚠️</text>
            <text class="tip-text">此操作将清除当日的时间安排、摸鱼记录和收入调整等所有数据，请谨慎操作</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置工作计划组件 -->
  <schedule-modal
    visible="{{showScheduleModal}}"
    selected-date="{{selectedDateKey}}"
    bind:dataUpdated="onScheduleDataUpdated"
    bind:cancel="onScheduleCancel"
    bind:close="onScheduleClose">
  </schedule-modal>

  <!-- 批量操作组件 -->
  <batch-operation-modal
    visible="{{showBatchModal}}"
    operation="{{batchOperation}}"
    bind:dataUpdated="onBatchOperationDataUpdated"
    bind:cancel="onBatchOperationCancel"
    bind:close="onBatchOperationClose">
  </batch-operation-modal>

  <!-- 日收入计算器组件 -->
  <daily-income-calculator
    visible="{{showDailyIncomeCalculatorModal}}"
    default-monthly-income="{{dailyIncomeCalculatorMonthlyIncome}}"
    default-work-days="{{dailyIncomeCalculatorWorkDays}}"
    target-mode="{{dailyIncomeCalculatorTargetMode}}"
    bind:confirm="onDailyIncomeCalculatorConfirm"
    bind:cancel="onDailyIncomeCalculatorCancel"
    bind:close="onDailyIncomeCalculatorClose">
  </daily-income-calculator>

  <!-- 摸鱼记录编辑器 -->
  <fishing-editor
    visible="{{showFishingEditor}}"
    mode="{{fishingEditorMode}}"
    fishing-data="{{editingFishing}}"
    segments="{{displaySegments}}"
    existing-fishes="{{displayFishes}}"
    bind:save="onFishingEditorSave"
    bind:delete="onFishingEditorDelete"
    bind:cancel="onFishingEditorCancel"
    bind:close="onFishingEditorClose">
  </fishing-editor>

  <!-- 日期类型选择器 -->
  <date-type-selector
    show="{{showDateTypeSelector}}"
    value="{{dateStatus}}"
    title="选择日期类型"
    bind:select="onDateTypeSelectorSelect"
    bind:confirm="onDateTypeSelectorConfirm"
    bind:cancel="onDateTypeSelectorCancel"
    bind:close="onDateTypeSelectorClose">
  </date-type-selector>

  <!-- 时间段选择器 -->
  <time-range-picker
    show="{{showTimeRangePicker}}"
    start-time="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].startTime : '09:00'}}"
    end-time="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].endTime : '18:00'}}"
    is-start-next-day="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].isStartNextDay : false}}"
    is-end-next-day="{{editingTimeIndex >= 0 ? timeInputs[editingTimeIndex].isEndNextDay : false}}"
    title="设置时间段"
    bind:confirm="onTimeRangePickerConfirm"
    bind:cancel="onTimeRangePickerCancel"
    bind:close="onTimeRangePickerClose">
  </time-range-picker>

  <!-- 收入调整模态框 -->
  <income-adjustment-modal
    visible="{{showIncomeAdjustmentModal}}"
    mode="{{adjustmentModalMode}}"
    date-string="{{adjustmentModalDateString}}"
    edit-item-id="{{adjustmentModalEditItemId}}"
    bind:dataUpdated="onIncomeAdjustmentDataUpdated"
    bind:close="onIncomeAdjustmentModalClose">
  </income-adjustment-modal>
</view>