# 主题统计功能测试指南

## 🧪 测试步骤

### 1. 文案修改验证
- [ ] 打开小程序，进入主页面
- [ ] 点击主题切换按钮
- [ ] 验证弹窗标题显示为"选择主题"
- [ ] 验证主题名称：
  - [ ] "简约主题" (原经典仪表盘)
  - [ ] "卡片主题" (原现代仪表盘)
- [ ] 验证主题描述：
  - [ ] 简约主题："简洁清爽的界面设计，专注核心功能"
  - [ ] 卡片主题："现代化卡片设计，信息层次分明"

### 2. 统计功能验证
- [ ] 打开主题选择器
- [ ] 观察是否显示"统计加载中..."
- [ ] 等待加载完成，验证统计信息格式：
  - [ ] 显示格式："{数量}人使用 ({百分比}%)"
  - [ ] 数字格式化正确（如：10+, 50+, 1.2k+, 1.5万+）
  - [ ] 百分比计算正确（两个主题百分比之和应为100%）

### 3. 缓存机制验证
- [ ] 第一次打开主题选择器，记录加载时间
- [ ] 关闭后立即重新打开，验证是否从缓存加载（应该更快）
- [ ] 切换主题后，再次打开验证统计数据是否更新

### 4. 边界情况测试
- [ ] 网络断开时打开主题选择器
- [ ] 验证错误处理是否正常
- [ ] 验证是否有合适的占位符显示

## 🔧 调试信息

### 控制台日志关键词
- `[ThemeStats]` - 云函数统计相关日志
- `选择主题:` - 前端主题切换日志
- `主题统计完成:` - 统计计算完成日志

### 预期的API响应格式
```json
{
  "success": true,
  "data": {
    "dashboard1": {
      "count": 123,
      "percentage": 45.6,
      "displayCount": "120+"
    },
    "dashboard2": {
      "count": 147,
      "percentage": 54.4,
      "displayCount": "145+"
    },
    "total": 270,
    "lastUpdated": "2025-01-08T10:30:00.000Z"
  }
}
```

## 🐛 常见问题排查

1. **统计数据不显示**
   - 检查云函数是否部署成功
   - 检查网络连接
   - 查看控制台错误日志

2. **数据格式化异常**
   - 检查 `formatUserCount` 函数逻辑
   - 验证数据类型是否正确

3. **缓存不生效**
   - 检查缓存数据库操作
   - 验证缓存过期时间设置

4. **主题切换后统计不更新**
   - 检查缓存清除逻辑
   - 验证云函数调用是否成功
